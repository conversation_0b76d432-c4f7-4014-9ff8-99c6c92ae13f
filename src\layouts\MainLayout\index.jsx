import React, { useState } from "react";
import { Sidebar, Menu, MenuItem } from "react-pro-sidebar";
import { Link, Outlet, useLocation } from "react-router-dom";
import { CiGrid41 } from "react-icons/ci";
import ROUTES from "@constants/routes";
import Logo from "../../assets/images/logo.png";

const MainLayout = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [toggled, setToggled] = useState(false);
  const location = useLocation();

  return (
    <div className="d-flex vh-100">
      <Sidebar
        breakPoint="md"
        width="280px"
        collapsed={collapsed}
        toggled={toggled}
        onBackdropClick={() => setToggled(false)}
      >
        <Menu closeOnClick className="h-100">
          <div className="d-flex flex-column align-items-center card bg-transparent border-0 p-3 text-center">
            <img
              src={Logo}
              alt="Logo"
              width={collapsed ? 70 : 220}
              height={collapsed ? 70 : 100}
              loading="lazy"
            />
          </div>
          <MenuItem
            icon={<CiGrid41 size={24} />}
            active={location.pathname === ROUTES.HOME}
            component={<Link to={ROUTES.HOME} />}
          >
            Home
          </MenuItem>
        </Menu>
      </Sidebar>
      <div className="d-flex flex-column flex-grow-1 overflow-hidden">
        <div className="p-3 flex-grow-1 overflow-auto">
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default MainLayout;
