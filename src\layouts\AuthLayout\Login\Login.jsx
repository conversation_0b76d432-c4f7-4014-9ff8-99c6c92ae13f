import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import {
  <PERSON>a<PERSON>ser,
  FaLock,
  FaEye,
  FaEyeSlash,
  FaGoogle,
  FaGithub,
  FaApple,
  FaFingerprint,
  FaBolt,
  FaStar,
  FaHeart,
  FaShieldAlt,
} from "react-icons/fa";
import {
  MdDirectionsCar,
  MdSecurity,
  MdSpeed,
  MdAnalytics,
  MdTrendingUp,
  MdAutoAwesome,
  MdVerified,
  MdElectricBolt,
} from "react-icons/md";
import { HiSparkles, HiLightningBolt, HiShieldCheck } from "react-icons/hi";

const Login = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    rememberMe: false,
  });
  const navigate = useNavigate();

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000));

    console.log("Login attempt:", formData);
    setIsLoading(false);
    navigate("/dashboard");
  };

  const handleSocialLogin = (provider) => {
    console.log(`Login with ${provider}`);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.6, ease: "easeOut" },
    },
  };

  const floatingVariants = {
    animate: {
      y: [-20, 20, -20],
      rotate: [0, 5, -5, 0],
      transition: {
        duration: 6,
        repeat: Infinity,
        ease: "easeInOut",
      },
    },
  };

  return (
    <div className="modern-login-container">
      {/* Background Elements */}
      <div className="bg-elements">
        <div className="gradient-orb orb-1"></div>
        <div className="gradient-orb orb-2"></div>
        <div className="gradient-orb orb-3"></div>
        <div className="floating-shapes">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className={`floating-shape shape-${i + 1}`}
              variants={floatingVariants}
              animate="animate"
              style={{ animationDelay: `${i * 0.5}s` }}
            />
          ))}
        </div>
      </div>

      <div className="container-fluid h-100">
        <div className="row h-100 align-items-center">
          {/* Left Side - Modern Hero */}
          <motion.div
            className="col-lg-6 d-none d-lg-flex hero-section"
            initial={{ x: -100, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 1, ease: "easeOut" }}
          >
            <div className="hero-content">
              {/* Time Display */}
              <motion.div
                className="time-display"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
              >
                <div className="time">
                  {currentTime.toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </div>
                <div className="date">
                  {currentTime.toLocaleDateString([], {
                    weekday: "long",
                    month: "short",
                    day: "numeric",
                  })}
                </div>
              </motion.div>

              {/* Main Hero Content */}
              <motion.div
                className="hero-main"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
              >
                <motion.div className="hero-badge" variants={itemVariants}>
                  <HiSparkles className="badge-icon" />
                  <span>Next-Gen Vehicle Management</span>
                  <div className="badge-glow"></div>
                </motion.div>

                <motion.h1 className="hero-title" variants={itemVariants}>
                  Drive the Future of
                  <span className="gradient-text"> Fleet Management</span>
                </motion.h1>

                <motion.p className="hero-description" variants={itemVariants}>
                  Experience the power of AI-driven analytics, real-time
                  monitoring, and seamless automation in one revolutionary
                  platform.
                </motion.p>

                {/* Feature Pills */}
                <motion.div className="feature-pills" variants={itemVariants}>
                  <div className="pill">
                    <MdAutoAwesome className="pill-icon" />
                    <span>AI-Powered</span>
                  </div>
                  <div className="pill">
                    <HiLightningBolt className="pill-icon" />
                    <span>Real-time</span>
                  </div>
                  <div className="pill">
                    <HiShieldCheck className="pill-icon" />
                    <span>Secure</span>
                  </div>
                </motion.div>

                {/* Stats */}
                <motion.div className="hero-stats" variants={itemVariants}>
                  <div className="stat">
                    <div className="stat-number">
                      <MdTrendingUp className="stat-icon" />
                      98%
                    </div>
                    <div className="stat-label">Efficiency Boost</div>
                  </div>
                  <div className="stat">
                    <div className="stat-number">
                      <FaStar className="stat-icon" />
                      4.9
                    </div>
                    <div className="stat-label">User Rating</div>
                  </div>
                  <div className="stat">
                    <div className="stat-number">
                      <FaHeart className="stat-icon" />
                      50K+
                    </div>
                    <div className="stat-label">Happy Users</div>
                  </div>
                </motion.div>
              </motion.div>
            </div>
          </motion.div>

          {/* Right Side - Modern Login Form */}
          <motion.div
            className="col-lg-6 col-12 form-section"
            initial={{ x: 100, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 1, ease: "easeOut" }}
          >
            <div className="form-container">
              {/* Glass Card */}
              <motion.div
                className="glass-card"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
              >
                {/* Header */}
                <motion.div className="form-header" variants={itemVariants}>
                  <div className="brand-logo">
                    <div className="logo-wrapper">
                      <MdDirectionsCar className="logo-icon" />
                      <div className="logo-glow"></div>
                    </div>
                    <span className="brand-text">VMS</span>
                  </div>
                  <h2 className="form-title">Welcome back</h2>
                  <p className="form-subtitle">
                    Sign in to your account to continue
                  </p>
                </motion.div>

                {/* Form */}
                <motion.form
                  className="modern-form"
                  onSubmit={handleSubmit}
                  variants={itemVariants}
                >
                  {/* Email Field */}
                  <motion.div className="input-wrapper" variants={itemVariants}>
                    <div className="input-container">
                      <FaUser className="input-icon" />
                      <input
                        type="email"
                        name="email"
                        placeholder="Email address"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="modern-input"
                        required
                      />
                      <div className="input-border"></div>
                    </div>
                  </motion.div>

                  {/* Password Field */}
                  <motion.div className="input-wrapper" variants={itemVariants}>
                    <div className="input-container">
                      <FaLock className="input-icon" />
                      <input
                        type={showPassword ? "text" : "password"}
                        name="password"
                        placeholder="Password"
                        value={formData.password}
                        onChange={handleInputChange}
                        className="modern-input"
                        required
                      />
                      <button
                        type="button"
                        className="password-toggle-btn"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? <FaEyeSlash /> : <FaEye />}
                      </button>
                      <div className="input-border"></div>
                    </div>
                  </motion.div>

                  {/* Options */}
                  <motion.div className="form-options" variants={itemVariants}>
                    <label className="checkbox-container">
                      <input
                        type="checkbox"
                        name="rememberMe"
                        checked={formData.rememberMe}
                        onChange={handleInputChange}
                      />
                      <span className="checkmark"></span>
                      <span className="checkbox-text">Remember me</span>
                    </label>
                    <a href="#" className="forgot-link">
                      Forgot password?
                    </a>
                  </motion.div>

                  {/* Login Button */}
                  <motion.button
                    type="submit"
                    className="modern-btn primary-btn"
                    variants={itemVariants}
                    whileHover={{ scale: 1.02, y: -2 }}
                    whileTap={{ scale: 0.98 }}
                    disabled={isLoading}
                  >
                    <AnimatePresence mode="wait">
                      {isLoading ? (
                        <motion.div
                          key="loading"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          className="loading-content"
                        >
                          <div className="spinner"></div>
                          <span>Signing in...</span>
                        </motion.div>
                      ) : (
                        <motion.div
                          key="signin"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          className="btn-content"
                        >
                          <span>Sign in</span>
                          <HiLightningBolt className="btn-icon" />
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.button>

                  {/* Divider */}
                  <motion.div className="divider" variants={itemVariants}>
                    <span>or continue with</span>
                  </motion.div>

                  {/* Social Buttons */}
                  <motion.div
                    className="social-buttons"
                    variants={itemVariants}
                  >
                    <motion.button
                      type="button"
                      className="social-btn google-btn"
                      onClick={() => handleSocialLogin("google")}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <FaGoogle />
                    </motion.button>
                    <motion.button
                      type="button"
                      className="social-btn github-btn"
                      onClick={() => handleSocialLogin("github")}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <FaGithub />
                    </motion.button>
                    <motion.button
                      type="button"
                      className="social-btn apple-btn"
                      onClick={() => handleSocialLogin("apple")}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <FaApple />
                    </motion.button>
                  </motion.div>

                  {/* Biometric Option */}
                  <motion.div
                    className="biometric-option"
                    variants={itemVariants}
                  >
                    <motion.button
                      type="button"
                      className="biometric-btn"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <FaFingerprint />
                      <span>Use Touch ID</span>
                    </motion.button>
                  </motion.div>

                  {/* Sign Up Link */}
                  <motion.div className="signup-link" variants={itemVariants}>
                    <span>Don't have an account? </span>
                    <a href="#signup" className="signup-btn">
                      Sign up
                    </a>
                  </motion.div>
                </motion.form>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Login;
