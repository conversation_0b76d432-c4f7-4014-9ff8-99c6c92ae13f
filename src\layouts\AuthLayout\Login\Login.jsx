import React, { useState } from "react";

const Login = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    // Simulate login process
    setTimeout(() => {
      setIsLoading(false);
      console.log("Login attempt:", { email, password });
    }, 2000);
  };

  return (
    <div className="login-wrapper">
      <div className="container-fluid vh-100 p-0">
        <div className="row h-100 g-0">
          {/* Left Side - Gradient Background with Content */}
          <div className="col-lg-6 gradient-section d-flex align-items-center justify-content-center">
            <div className="content-wrapper text-white text-center">
              <div className="brand-logo mb-4">
                <div className="logo-circle">
                  <i className="bi bi-grid-3x3-gap"></i>
                </div>
                <span className="brand-text">VehicleHub</span>
              </div>

              <h1 className="main-title">One Tap to Everything You Need</h1>
              <p className="subtitle">
                Experience seamless access to everything you desire, curated
                with care.
              </p>
            </div>
          </div>

          {/* Right Side - Login Form */}
          <div className="col-lg-6 form-section d-flex align-items-center justify-content-center">
            <div className="form-container">
              <div className="text-center mb-4">
                <div className="brand-mini mb-3">
                  <i className="bi bi-grid-3x3-gap"></i>
                  <span>VehicleHub</span>
                </div>
                <h2 className="form-title">Sign In</h2>
                <p className="form-subtitle">
                  Sign in to unlock your personalized experience.
                </p>
              </div>

              <form onSubmit={handleSubmit}>
                <div className="mb-3">
                  <label className="form-label">Email *</label>
                  <input
                    type="email"
                    className="form-control custom-input"
                    placeholder="Enter your email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                  />
                </div>

                <div className="mb-4">
                  <label className="form-label">Password *</label>
                  <div className="position-relative">
                    <input
                      type={showPassword ? "text" : "password"}
                      className="form-control custom-input"
                      placeholder="Enter password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                    />
                    <button
                      type="button"
                      className="password-toggle"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      <i
                        className={`bi ${
                          showPassword ? "bi-eye-slash" : "bi-eye"
                        }`}
                      ></i>
                    </button>
                  </div>
                </div>

                <div className="d-flex justify-content-between align-items-center mb-4">
                  <div className="form-check">
                    <input
                      className="form-check-input custom-checkbox"
                      type="checkbox"
                      id="rememberMe"
                    />
                    <label
                      className="form-check-label small-text"
                      htmlFor="rememberMe"
                    >
                      I agree to the Terms & Conditions and Privacy Policy
                    </label>
                  </div>
                </div>

                <button
                  type="submit"
                  className="btn btn-dark w-100 custom-btn mb-3"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2"></span>
                      Signing In...
                    </>
                  ) : (
                    "Get Started"
                  )}
                </button>

                <button
                  type="button"
                  className="btn btn-outline-secondary w-100 google-btn mb-4"
                >
                  <i className="bi bi-google me-2"></i>
                  Sign In with Google
                </button>

                <div className="text-center">
                  <span className="small-text">Already have an account? </span>
                  <a href="#" className="link-primary">
                    Sign In
                  </a>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
