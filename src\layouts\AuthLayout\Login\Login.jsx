import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import Logo from "../../../assets/images/logo.png";
import { <PERSON>a<PERSON><PERSON>, <PERSON><PERSON><PERSON>ock, <PERSON>a<PERSON>ye, FaEyeSlash } from "react-icons/fa";
import { MdDirectionsCar } from "react-icons/md";

const Login = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [credentials, setCredentials] = useState({ email: "", password: "" });
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const handleChange = (e) => {
    setCredentials({ ...credentials, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsLoading(true);
    // Add your authentication logic here
    setTimeout(() => {
      setIsLoading(false);
      navigate("/");
    }, 1500);
  };

  return (
    <div className="container-fluid">
      <div className="row vh-100">
        {/* Left side - Login Form */}
        <div className="col-md-5 d-flex align-items-center justify-content-center">
          <div
            className="card border-0 shadow-sm p-4 w-100"
            style={{ maxWidth: "450px" }}
          >
            <div className="text-center mb-4">
              <img src={Logo} alt="Logo" className="mb-3" width="180" />
              <h4 className="fw-bold text-primary">Welcome Back</h4>
              <p className="text-muted">Sign in to continue to TRITRACKZ</p>
            </div>

            <form onSubmit={handleSubmit}>
              <div className="form-floating mb-3">
                <input
                  type="email"
                  className="form-control"
                  id="email"
                  name="email"
                  placeholder="<EMAIL>"
                  value={credentials.email}
                  onChange={handleChange}
                  required
                />
                <label htmlFor="email">
                  <FaUser className="me-2" size={14} /> Email Address
                </label>
              </div>

              <div className="form-floating mb-4 position-relative">
                <input
                  type={showPassword ? "text" : "password"}
                  className="form-control"
                  id="password"
                  name="password"
                  placeholder="Password"
                  value={credentials.password}
                  onChange={handleChange}
                  required
                />
                <label htmlFor="password">
                  <FaLock className="me-2" size={14} /> Password
                </label>
                <button
                  type="button"
                  className="btn position-absolute end-0 top-50 translate-middle-y bg-transparent border-0 text-muted"
                  onClick={() => setShowPassword(!showPassword)}
                  style={{ zIndex: 5 }}
                >
                  {showPassword ? (
                    <FaEyeSlash size={16} />
                  ) : (
                    <FaEye size={16} />
                  )}
                </button>
              </div>

              <div className="d-flex justify-content-between mb-4">
                <div className="form-check">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    id="rememberMe"
                  />
                  <label
                    className="form-check-label text-muted"
                    htmlFor="rememberMe"
                  >
                    Remember me
                  </label>
                </div>
                <a href="#" className="text-decoration-none">
                  Forgot password?
                </a>
              </div>

              <button
                type="submit"
                className="btn btn-primary w-100 py-2 mb-3"
                disabled={isLoading}
              >
                {isLoading ? (
                  <span
                    className="spinner-border spinner-border-sm me-2"
                    role="status"
                    aria-hidden="true"
                  ></span>
                ) : null}
                Sign In
              </button>
            </form>
          </div>
        </div>

        {/* Right side - Background Image */}
        <div className="col-md-7 d-none d-md-flex align-items-center justify-content-center bg-primary bg-gradient position-relative">
          <div className="text-white text-center p-5">
            <MdDirectionsCar size={80} className="mb-4" />
            <h2 className="fw-bold mb-4">Vehicle Management System</h2>
            <p className="lead mb-4">
              Track, manage, and optimize your fleet operations with our
              comprehensive solution
            </p>
            <div className="d-flex justify-content-center gap-3">
              <div
                className="bg-white bg-opacity-10 rounded-3 p-3 text-center"
                style={{ width: "120px" }}
              >
                <h3 className="mb-1">100+</h3>
                <p className="mb-0 small">Vehicles</p>
              </div>
              <div
                className="bg-white bg-opacity-10 rounded-3 p-3 text-center"
                style={{ width: "120px" }}
              >
                <h3 className="mb-1">24/7</h3>
                <p className="mb-0 small">Tracking</p>
              </div>
              <div
                className="bg-white bg-opacity-10 rounded-3 p-3 text-center"
                style={{ width: "120px" }}
              >
                <h3 className="mb-1">99%</h3>
                <p className="mb-0 small">Uptime</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
