import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import Logo from "../../../assets/images/logo.png";
import {
  FaUser,
  FaLock,
  FaEye,
  FaEyeSlash,
  FaShieldAlt,
  FaRocket,
  FaClock,
} from "react-icons/fa";
import {
  MdDirectionsCar,
  MdSecurity,
  MdSpeed,
  MdAnalytics,
} from "react-icons/md";

const Login = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [credentials, setCredentials] = useState({ email: "", password: "" });
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const handleChange = (e) => {
    setCredentials({ ...credentials, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsLoading(true);
    // Add your authentication logic here
    setTimeout(() => {
      setIsLoading(false);
      navigate("/");
    }, 1500);
  };

  return (
    <div className="login-container">
      <div className="container-fluid h-100">
        <div className="row h-100 g-0">
          {/* Left side - Login Form */}
          <div className="col-lg-5 col-md-6 d-flex align-items-center justify-content-center p-4">
            <div
              className="modern-card form-container p-5 w-100"
              style={{ maxWidth: "480px" }}
            >
              {/* Logo and Header */}
              <div className="text-center mb-5 logo-container">
                <img
                  src={Logo}
                  alt="TRITRACKZ Logo"
                  className="mb-4"
                  width="200"
                  style={{ filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.1))" }}
                />
                <h2
                  className="fw-bold mb-2"
                  style={{ color: "var(--text-primary)" }}
                >
                  Welcome Back
                </h2>
                <p className="text-muted mb-0">
                  Sign in to access your vehicle management dashboard
                </p>
              </div>

              {/* Login Form */}
              <form onSubmit={handleSubmit}>
                {/* Email Input */}
                <div className="modern-input-group">
                  <input
                    type="email"
                    className="modern-input"
                    id="email"
                    name="email"
                    placeholder="Enter your email address"
                    value={credentials.email}
                    onChange={handleChange}
                    required
                  />
                  <FaUser className="input-icon" size={16} />
                </div>

                {/* Password Input */}
                <div className="modern-input-group">
                  <input
                    type={showPassword ? "text" : "password"}
                    className="modern-input"
                    id="password"
                    name="password"
                    placeholder="Enter your password"
                    value={credentials.password}
                    onChange={handleChange}
                    required
                  />
                  <FaLock className="input-icon" size={16} />
                  <button
                    type="button"
                    className="password-toggle"
                    onClick={() => setShowPassword(!showPassword)}
                    aria-label={
                      showPassword ? "Hide password" : "Show password"
                    }
                  >
                    {showPassword ? (
                      <FaEyeSlash size={16} />
                    ) : (
                      <FaEye size={16} />
                    )}
                  </button>
                </div>

                {/* Remember Me and Forgot Password */}
                <div className="d-flex justify-content-between align-items-center mb-4">
                  <label className="modern-checkbox d-flex align-items-center cursor-pointer">
                    <input type="checkbox" id="rememberMe" />
                    <span className="checkmark"></span>
                    <span className="ms-3 text-muted">Remember me</span>
                  </label>
                  <a href="#" className="modern-link">
                    Forgot password?
                  </a>
                </div>

                {/* Sign In Button */}
                <button
                  type="submit"
                  className="modern-btn w-100 d-flex align-items-center justify-content-center"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <div className="modern-spinner me-2"></div>
                      Signing In...
                    </>
                  ) : (
                    "Sign In"
                  )}
                </button>
              </form>

              {/* Additional Links */}
              <div className="text-center mt-4">
                <p className="text-muted small mb-0">
                  Don't have an account?
                  <a href="#" className="modern-link ms-1">
                    Contact Administrator
                  </a>
                </p>
              </div>
            </div>
          </div>

          {/* Right side - Hero Section */}
          <div className="col-lg-7 col-md-6 d-none d-md-flex hero-panel">
            <div className="hero-content d-flex flex-column justify-content-center align-items-center text-white p-5 w-100">
              {/* Main Hero Content */}
              <div className="text-center mb-5">
                <div className="mb-4">
                  <MdDirectionsCar
                    size={100}
                    className="mb-3"
                    style={{ filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.2))" }}
                  />
                </div>
                <h1 className="display-4 fw-bold mb-4">
                  Vehicle Management
                  <br />
                  <span
                    style={{
                      background: "linear-gradient(45deg, #fff, #e2e8f0)",
                      WebkitBackgroundClip: "text",
                      WebkitTextFillColor: "transparent",
                    }}
                  >
                    System
                  </span>
                </h1>
                <p className="lead mb-5 opacity-90">
                  Streamline your fleet operations with our comprehensive
                  tracking, management, and analytics platform designed for
                  modern businesses.
                </p>
              </div>

              {/* Feature Stats */}
              <div className="row g-4 w-100" style={{ maxWidth: "600px" }}>
                <div className="col-4">
                  <div className="stat-card text-center">
                    <MdSecurity size={32} className="mb-3" />
                    <h3 className="h4 mb-2">100+</h3>
                    <p className="small mb-0 opacity-90">Vehicles Tracked</p>
                  </div>
                </div>
                <div className="col-4">
                  <div className="stat-card text-center">
                    <MdSpeed size={32} className="mb-3" />
                    <h3 className="h4 mb-2">24/7</h3>
                    <p className="small mb-0 opacity-90">
                      Real-time Monitoring
                    </p>
                  </div>
                </div>
                <div className="col-4">
                  <div className="stat-card text-center">
                    <MdAnalytics size={32} className="mb-3" />
                    <h3 className="h4 mb-2">99.9%</h3>
                    <p className="small mb-0 opacity-90">System Uptime</p>
                  </div>
                </div>
              </div>

              {/* Feature Highlights */}
              <div className="row g-3 mt-4 w-100" style={{ maxWidth: "600px" }}>
                <div className="col-md-4">
                  <div className="d-flex align-items-center">
                    <FaShieldAlt className="me-3" size={20} />
                    <span className="small">Secure & Reliable</span>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="d-flex align-items-center">
                    <FaRocket className="me-3" size={20} />
                    <span className="small">Fast Performance</span>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="d-flex align-items-center">
                    <FaClock className="me-3" size={20} />
                    <span className="small">24/7 Support</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
