import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import {
  <PERSON>a<PERSON>ser,
  FaLock,
  FaEye,
  FaEyeSlash,
  FaGoogle,
  FaShieldAlt,
  FaRocket,
  FaCheckCircle,
  FaCar,
  FaChartLine,
  FaTachometerAlt,
  FaMapMarkerAlt,
} from "react-icons/fa";
import {
  MdDirectionsCar,
  MdDashboard,
  MdLocationOn,
  MdSecurity,
  MdSpeed,
  MdAnalytics,
  //   MdFleetManagement,
  MdGpsFixed,
} from "react-icons/md";

const Login = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    rememberMe: false,
  });
  const navigate = useNavigate();

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle login logic here
    console.log("Login attempt:", formData);
    navigate("/dashboard");
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 },
    },
  };

  const floatingVariants = {
    animate: {
      y: [-10, 10, -10],
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut",
      },
    },
  };

  return (
    <div className="login-container">
      <div className="container-fluid h-100">
        <div className="row h-100">
          {/* Left Side - Enhanced Hero Section */}
          <motion.div
            className="col-lg-7 d-none d-lg-flex login-hero"
            initial={{ x: -100, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
          >
            <div className="hero-content">
              <motion.div
                className="hero-background"
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 1, delay: 0.2 }}
              >
                <div className="gradient-overlay">
                  <div className="overlay-pattern"></div>
                  <div className="overlay-dots"></div>
                  <div className="overlay-mesh"></div>
                </div>
                <div className="animated-shapes">
                  <motion.div
                    className="shape shape-1"
                    variants={floatingVariants}
                    animate="animate"
                  >
                    <div className="shape-inner">
                      <MdDirectionsCar className="shape-icon" />
                    </div>
                  </motion.div>
                  <motion.div
                    className="shape shape-2"
                    variants={floatingVariants}
                    animate="animate"
                    style={{ animationDelay: "1s" }}
                  >
                    <div className="shape-inner">
                      <MdAnalytics className="shape-icon" />
                    </div>
                  </motion.div>
                  <motion.div
                    className="shape shape-3"
                    variants={floatingVariants}
                    animate="animate"
                    style={{ animationDelay: "2s" }}
                  >
                    <div className="shape-inner">
                      <MdSecurity className="shape-icon" />
                    </div>
                  </motion.div>
                  <motion.div
                    className="shape shape-4"
                    variants={floatingVariants}
                    animate="animate"
                    style={{ animationDelay: "0.5s" }}
                  >
                    <div className="shape-inner">
                      <MdSpeed className="shape-icon" />
                    </div>
                  </motion.div>
                  <motion.div
                    className="shape shape-5"
                    variants={floatingVariants}
                    animate="animate"
                    style={{ animationDelay: "1.5s" }}
                  >
                    <div className="shape-inner">
                      <MdGpsFixed className="shape-icon" />
                    </div>
                  </motion.div>
                </div>
              </motion.div>

              <div className="hero-text">
                <motion.div
                  className="hero-badge"
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.8, delay: 0.3 }}
                >
                  <FaRocket className="badge-icon" />
                  <span>Advanced Fleet Management</span>
                </motion.div>

                <motion.h1
                  className="hero-title"
                  initial={{ y: 50, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                >
                  Complete Vehicle Management Solution
                </motion.h1>
                <motion.p
                  className="hero-subtitle"
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                >
                  Experience seamless fleet management with advanced tracking,
                  real-time analytics, and comprehensive monitoring capabilities
                  designed for modern businesses.
                </motion.p>

                <motion.div
                  className="hero-features"
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.8, delay: 0.8 }}
                >
                  <div className="feature-item">
                    <div className="feature-icon-wrapper">
                      <MdDirectionsCar className="feature-icon" />
                    </div>
                    <div className="feature-content">
                      <h4>Fleet Tracking</h4>
                      <p>Real-time vehicle monitoring</p>
                    </div>
                  </div>
                  <div className="feature-item">
                    <div className="feature-icon-wrapper">
                      <MdAnalytics className="feature-icon" />
                    </div>
                    <div className="feature-content">
                      <h4>Smart Analytics</h4>
                      <p>Data-driven insights</p>
                    </div>
                  </div>
                  <div className="feature-item">
                    <div className="feature-icon-wrapper">
                      <MdSecurity className="feature-icon" />
                    </div>
                    <div className="feature-content">
                      <h4>Security</h4>
                      <p>Advanced protection</p>
                    </div>
                  </div>
                  <div className="feature-item">
                    <div className="feature-icon-wrapper">
                      <MdSpeed className="feature-icon" />
                    </div>
                    <div className="feature-content">
                      <h4>Performance</h4>
                      <p>Optimized operations</p>
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  className="hero-stats"
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.8, delay: 1.0 }}
                >
                  <div className="stat-item">
                    <div className="stat-number">10K+</div>
                    <div className="stat-label">Vehicles Managed</div>
                  </div>
                  <div className="stat-item">
                    <div className="stat-number">500+</div>
                    <div className="stat-label">Happy Clients</div>
                  </div>
                  <div className="stat-item">
                    <div className="stat-number">99.9%</div>
                    <div className="stat-label">Uptime</div>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>

          {/* Right Side - Login Form */}
          <motion.div
            className="col-lg-5 col-12 login-form-section"
            initial={{ x: 100, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
          >
            <div className="login-form-container">
              <motion.div
                className="brand-section"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
              >
                <motion.div className="brand-logo" variants={itemVariants}>
                  <MdDirectionsCar className="logo-icon" />
                  <span className="brand-name">VMS Pro</span>
                </motion.div>
              </motion.div>

              <motion.div
                className="form-content"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
              >
                <motion.div className="form-header" variants={itemVariants}>
                  <h2 className="form-title">Welcome Back</h2>
                  <p className="form-subtitle">
                    Sign in to access your vehicle management dashboard
                  </p>
                </motion.div>

                <motion.form
                  className="login-form"
                  onSubmit={handleSubmit}
                  variants={itemVariants}
                >
                  <motion.div className="form-group" variants={itemVariants}>
                    <label htmlFor="email" className="form-label">
                      Email Address <span className="required">*</span>
                    </label>
                    <div className="input-group">
                      <span className="input-icon">
                        <FaUser />
                      </span>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        className="form-control"
                        placeholder="Enter your email address"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                  </motion.div>

                  <motion.div className="form-group" variants={itemVariants}>
                    <label htmlFor="password" className="form-label">
                      Password <span className="required">*</span>
                    </label>
                    <div className="input-group">
                      <span className="input-icon">
                        <FaLock />
                      </span>
                      <input
                        type={showPassword ? "text" : "password"}
                        id="password"
                        name="password"
                        className="form-control"
                        placeholder="Enter your password"
                        value={formData.password}
                        onChange={handleInputChange}
                        required
                      />
                      <button
                        type="button"
                        className="password-toggle"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? <FaEyeSlash /> : <FaEye />}
                      </button>
                    </div>
                  </motion.div>

                  <motion.div className="form-options" variants={itemVariants}>
                    <div className="form-check">
                      <input
                        type="checkbox"
                        id="rememberMe"
                        name="rememberMe"
                        className="form-check-input"
                        checked={formData.rememberMe}
                        onChange={handleInputChange}
                      />
                      <label htmlFor="rememberMe" className="form-check-label">
                        Remember me
                      </label>
                    </div>
                    <a href="#" className="forgot-password">
                      Forgot Password?
                    </a>
                  </motion.div>

                  <motion.button
                    type="submit"
                    className="btn btn-primary login-btn"
                    variants={itemVariants}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Sign In
                  </motion.button>

                  <motion.div className="divider" variants={itemVariants}>
                    <span>or</span>
                  </motion.div>

                  <motion.button
                    type="button"
                    className="btn btn-outline-secondary google-btn"
                    variants={itemVariants}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <FaGoogle className="me-2" />
                    Sign in with Google
                  </motion.button>

                  <motion.div className="signup-link" variants={itemVariants}>
                    Don't have an account? <a href="#signup">Sign Up</a>
                  </motion.div>
                </motion.form>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Login;
