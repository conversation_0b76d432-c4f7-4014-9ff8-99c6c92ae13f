.login-wrapper {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: #f8f9fa;
  }

  /* Left Gradient Section */
  .gradient-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    position: relative;
    overflow: hidden;
  }

  .gradient-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="stripes" patternUnits="userSpaceOnUse" width="10" height="10" patternTransform="rotate(45)"><rect width="5" height="10" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23stripes)"/></svg>');
    opacity: 0.3;
  }

  .content-wrapper {
    position: relative;
    z-index: 2;
    max-width: 400px;
    padding: 2rem;
  }

  .brand-logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .logo-circle {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .brand-text {
    font-size: 1.2rem;
    font-weight: 600;
    letter-spacing: 0.5px;
  }

  .main-title {
    font-size: 3rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
  }

  .subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    line-height: 1.6;
    font-weight: 300;
  }

  /* Right Form Section */
  .form-section {
    background: white;
  }

  .form-container {
    width: 100%;
    max-width: 400px;
    padding: 2rem;
  }

  .brand-mini {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
  }

  .brand-mini i {
    font-size: 1.2rem;
  }

  .form-title {
    font-size: 2rem;
    font-weight: 700;
    color: #212529;
    margin-bottom: 0.5rem;
  }

  .form-subtitle {
    color: #6c757d;
    font-size: 0.95rem;
    margin-bottom: 0;
  }

  .form-label {
    font-weight: 600;
    color: #212529;
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
  }

  .custom-input {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 0.8rem 1rem;
    font-size: 0.95rem;
    background: #f8f9fa;
    transition: all 0.2s ease;
    height: 48px;
  }

  .custom-input:focus {
    background: white;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }

  .custom-input::placeholder {
    color: #adb5bd;
    font-size: 0.9rem;
  }

  .password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #adb5bd;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
  }

  .password-toggle:hover {
    color: #6c757d;
  }

  .custom-checkbox {
    border-radius: 4px;
    border: 1px solid #dee2e6;
  }

  .custom-checkbox:checked {
    background-color: #212529;
    border-color: #212529;
  }

  .small-text {
    font-size: 0.8rem;
    color: #6c757d;
  }

  .custom-btn {
    background: #212529;
    border: none;
    border-radius: 8px;
    padding: 0.8rem;
    font-weight: 600;
    font-size: 0.95rem;
    height: 48px;
    transition: all 0.2s ease;
  }

  .custom-btn:hover:not(:disabled) {
    background: #000;
    transform: translateY(-1px);
  }

  .google-btn {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 0.8rem;
    font-weight: 500;
    font-size: 0.95rem;
    height: 48px;
    color: #495057;
    transition: all 0.2s ease;
  }

  .google-btn:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
    color: #495057;
  }

  .link-primary {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
  }

  .link-primary:hover {
    color: #5a67d8;
    text-decoration: underline;
  }

  /* Responsive Design */
  @media (max-width: 991.98px) {
    .main-title {
      font-size: 2.5rem;
    }
    
    .form-container {
      padding: 1.5rem;
    }
  }

  @media (max-width: 576px) {
    .main-title {
      font-size: 2rem;
    }
    
    .form-title {
      font-size: 1.5rem;
    }

    .content-wrapper {
      padding: 1.5rem;
    }
  }
