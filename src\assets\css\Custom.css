/* Ultra Modern Login Screen - 2024 Trending Design */

/* CSS Variables for Dynamic Theming */
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --text-primary: #1a1a1a;
  --text-secondary: #6b7280;
  --text-light: rgba(255, 255, 255, 0.9);
  --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
  --shadow-heavy: 0 20px 60px rgba(0, 0, 0, 0.1);
  --border-radius: 20px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Reset & Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen, Ubuntu, Cantarell, sans-serif;
  font-weight: 400;
  line-height: 1.6;
  color: var(--text-primary);
  overflow-x: hidden;
  background: #0f0f23;
}

/* Modern Login Container */
.modern-login-container {
  min-height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  overflow: hidden;
}

/* Background Elements */
.bg-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* Gradient Orbs */
.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(100px);
  opacity: 0.7;
  animation: float 6s ease-in-out infinite;
}

.orb-1 {
  width: 400px;
  height: 400px;
  background: var(--primary-gradient);
  top: -200px;
  left: -200px;
  animation-delay: 0s;
}

.orb-2 {
  width: 300px;
  height: 300px;
  background: var(--secondary-gradient);
  top: 50%;
  right: -150px;
  animation-delay: 2s;
}

.orb-3 {
  width: 250px;
  height: 250px;
  background: var(--accent-gradient);
  bottom: -125px;
  left: 30%;
  animation-delay: 4s;
}

/* Floating Shapes */
.floating-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-shape {
  position: absolute;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.shape-1 {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  top: 20%;
  left: 10%;
  transform: rotate(45deg);
}

.shape-2 {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  top: 60%;
  left: 20%;
}

.shape-3 {
  width: 100px;
  height: 100px;
  border-radius: 30px;
  top: 30%;
  right: 15%;
  transform: rotate(-30deg);
}

.shape-4 {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  bottom: 30%;
  left: 15%;
  transform: rotate(60deg);
}

.shape-5 {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  bottom: 20%;
  right: 25%;
}

.shape-6 {
  width: 50px;
  height: 50px;
  border-radius: 15px;
  top: 10%;
  right: 30%;
  transform: rotate(-45deg);
}

/* Hero Section */
.hero-section {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
  z-index: 2;
}

.hero-content {
  max-width: 500px;
  width: 100%;
}

/* Time Display */
.time-display {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1rem 1.5rem;
  margin-bottom: 2rem;
  text-align: center;
}

.time {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-light);
  margin-bottom: 0.25rem;
}

.date {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Hero Badge */
.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  margin-bottom: 1.5rem;
  position: relative;
  overflow: hidden;
}

.badge-icon {
  font-size: 1rem;
  color: #fbbf24;
}

.hero-badge span {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-light);
}

.badge-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shimmer 2s infinite;
}

/* Hero Title */
.hero-title {
  font-size: 3rem;
  font-weight: 800;
  line-height: 1.1;
  color: var(--text-light);
  margin-bottom: 1rem;
}

.gradient-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Hero Description */
.hero-description {
  font-size: 1.125rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2rem;
  line-height: 1.6;
}

/* Feature Pills */
.feature-pills {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.pill {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  font-size: 0.875rem;
  color: var(--text-light);
  transition: var(--transition);
}

.pill:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.pill-icon {
  font-size: 1rem;
}

/* Hero Stats */
.hero-stats {
  display: flex;
  gap: 2rem;
  margin-top: 2rem;
}

.stat {
  text-align: center;
}

.stat-number {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-light);
  margin-bottom: 0.25rem;
}

.stat-icon {
  font-size: 1.25rem;
  color: #10b981;
}

.stat-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Form Section */
.form-section {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
  z-index: 2;
}

.form-container {
  width: 100%;
  max-width: 420px;
}

/* Glass Card */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius);
  padding: 2.5rem;
  box-shadow: var(--shadow-light);
  position: relative;
  overflow: hidden;
}

.glass-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.5),
    transparent
  );
}

/* Form Header */
.form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.brand-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.logo-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: var(--primary-gradient);
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.logo-icon {
  font-size: 1.5rem;
  color: white;
  z-index: 2;
}

.logo-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--primary-gradient);
  border-radius: 17px;
  filter: blur(8px);
  opacity: 0.7;
  z-index: 1;
}

.brand-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-light);
}

.form-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-light);
  margin-bottom: 0.5rem;
}

.form-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
}

/* Modern Form */
.modern-form {
  width: 100%;
}

.input-wrapper {
  margin-bottom: 1.5rem;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 1rem;
  z-index: 2;
  color: rgba(255, 255, 255, 0.5);
  font-size: 1rem;
  transition: var(--transition);
}

.modern-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: var(--text-light);
  font-size: 1rem;
  transition: var(--transition);
  backdrop-filter: blur(10px);
}

.modern-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.modern-input:focus {
  outline: none;
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.modern-input:focus + .input-border {
  transform: scaleX(1);
}

.modern-input:focus ~ .input-icon {
  color: #667eea;
}

.input-border {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--primary-gradient);
  transform: scaleX(0);
  transition: transform 0.3s ease;
  border-radius: 0 0 12px 12px;
}

.password-toggle-btn {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  font-size: 1rem;
  z-index: 2;
  transition: var(--transition);
}

.password-toggle-btn:hover {
  color: #667eea;
}

/* Animations */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    display: none;
  }

  .form-section {
    padding: 1rem;
  }

  .glass-card {
    padding: 2rem 1.5rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .time {
    font-size: 1.5rem;
  }

  .social-buttons {
    flex-direction: column;
    gap: 0.75rem;
  }

  .hero-stats {
    gap: 1rem;
  }

  .stat-number {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .glass-card {
    padding: 1.5rem 1rem;
  }

  .form-title {
    font-size: 1.5rem;
  }

  .modern-input {
    padding: 0.875rem 0.875rem 0.875rem 2.5rem;
  }

  .input-icon {
    left: 0.75rem;
  }

  .password-toggle-btn {
    right: 0.75rem;
  }
}
