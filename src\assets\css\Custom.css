/* Professional Login Design Styles */

/* Custom CSS Variables for consistent theming */
:root {
  /* Professional Blue-to-Indigo Gradient */
  --primary-gradient: linear-gradient(
    135deg,
    #4f46e5 0%,
    #7c3aed 50%,
    #2563eb 100%
  );
  --primary-gradient-hover: linear-gradient(
    135deg,
    #4338ca 0%,
    #6d28d9 50%,
    #1d4ed8 100%
  );

  /* Subtle accent gradients */
  --secondary-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --background-gradient: linear-gradient(
    135deg,
    #f1f5f9 0%,
    #e2e8f0 50%,
    #cbd5e1 100%
  );

  /* Glass morphism effects */
  --glass-bg: rgba(255, 255, 255, 0.85);
  --glass-border: rgba(255, 255, 255, 0.3);
  --glass-shadow: 0 8px 32px 0 rgba(15, 23, 42, 0.1);

  /* Professional shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Professional color palette */
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --border-color: #e2e8f0;
  --border-focus: #4f46e5;
  --bg-white: #ffffff;
  --bg-gray-50: #f8fafc;
  --bg-gray-100: #f1f5f9;

  /* Border radius */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;

  /* Professional transitions */
  --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  background: var(--background-gradient);
  min-height: 100vh;
  margin: 0;
  padding: 0;
  line-height: 1.6;
  color: var(--text-primary);
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Login Container Styles */
.login-container {
  min-height: 100vh;
  background: var(--background-gradient);
  position: relative;
  overflow: hidden;
}

.login-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(79,70,229,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
  opacity: 1;
  animation: backgroundFloat 20s ease-in-out infinite;
}

@keyframes backgroundFloat {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(1deg);
  }
}

/* Modern Card Styles */
.modern-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
  animation: cardSlideIn 0.6s ease-out;
}

.modern-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-gradient);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl), 0 0 0 1px rgba(79, 70, 229, 0.1);
  border-color: rgba(79, 70, 229, 0.2);
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Form Input Styles */
.modern-input-group {
  position: relative;
  margin-bottom: 1.5rem;
}

.modern-input {
  width: 100%;
  padding: 18px 24px 18px 52px;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 16px;
  font-weight: 400;
  transition: var(--transition-normal);
  background: var(--bg-white);
  color: var(--text-primary);
  line-height: 1.5;
}

.modern-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  background: var(--bg-white);
  transform: translateY(-1px);
}

.modern-input::placeholder {
  color: var(--text-muted);
  transition: var(--transition-normal);
  font-weight: 400;
}

.modern-input:focus::placeholder {
  opacity: 0.7;
  transform: translateY(-2px);
}

.modern-input:hover:not(:focus) {
  border-color: var(--text-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.input-icon {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  transition: var(--transition-normal);
  z-index: 2;
}

.modern-input:focus + .input-icon {
  color: var(--border-focus);
  transform: translateY(-50%) scale(1.1);
}

.modern-input:hover:not(:focus) + .input-icon {
  color: var(--text-secondary);
}

/* Password Toggle Button */
.password-toggle {
  position: absolute;
  right: 18px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 10px;
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-toggle:hover {
  background: rgba(79, 70, 229, 0.1);
  color: var(--border-focus);
  transform: translateY(-50%) scale(1.1);
}

.password-toggle:active {
  transform: translateY(-50%) scale(0.95);
}

/* Modern Button Styles */
.modern-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-md);
  padding: 18px 32px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  min-height: 56px;
}

.modern-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.6s ease;
}

.modern-btn:hover {
  background: var(--primary-gradient-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl), 0 0 20px rgba(79, 70, 229, 0.3);
}

.modern-btn:hover::before {
  left: 100%;
}

.modern-btn:active {
  transform: translateY(-1px);
}

.modern-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-md);
}

.modern-btn:disabled:hover {
  background: var(--primary-gradient);
  transform: none;
  box-shadow: var(--shadow-md);
}

/* Loading Spinner */
.modern-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: modernSpin 1s linear infinite;
}

@keyframes modernSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Checkbox Styles */
.modern-checkbox {
  position: relative;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.modern-checkbox input[type="checkbox"] {
  opacity: 0;
  position: absolute;
  width: 0;
  height: 0;
}

.modern-checkbox .checkmark {
  position: relative;
  height: 20px;
  width: 20px;
  background: var(--bg-white);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.modern-checkbox:hover .checkmark {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.modern-checkbox input:checked ~ .checkmark {
  background: var(--primary-gradient);
  border-color: var(--border-focus);
  transform: scale(1.05);
}

.modern-checkbox .checkmark:after {
  content: "";
  position: absolute;
  display: none;
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.modern-checkbox input:checked ~ .checkmark:after {
  display: block;
  animation: checkmarkSlide 0.2s ease-out;
}

@keyframes checkmarkSlide {
  0% {
    opacity: 0;
    transform: rotate(45deg) scale(0.5);
  }
  100% {
    opacity: 1;
    transform: rotate(45deg) scale(1);
  }
}

/* Link Styles */
.modern-link {
  color: var(--border-focus);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition-normal);
  position: relative;
  display: inline-block;
}

.modern-link::after {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background: var(--primary-gradient);
  transition: width 0.3s ease;
  border-radius: 1px;
}

.modern-link:hover::after {
  width: 100%;
}

.modern-link:hover {
  color: var(--border-focus);
  transform: translateY(-1px);
}

/* Right Panel Styles */
.hero-panel {
  background: var(--primary-gradient);
  position: relative;
  overflow: hidden;
}

.hero-panel::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="heroGrid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100%" height="100%" fill="url(%23heroGrid)"/></svg>');
  opacity: 0.4;
  animation: heroFloat 25s ease-in-out infinite;
}

@keyframes heroFloat {
  0%,
  100% {
    transform: translateX(0px) translateY(0px);
  }
  25% {
    transform: translateX(10px) translateY(-5px);
  }
  50% {
    transform: translateX(-5px) translateY(-10px);
  }
  75% {
    transform: translateX(-10px) translateY(5px);
  }
}

.hero-content {
  position: relative;
  z-index: 2;
  animation: heroSlideIn 0.8s ease-out 0.3s both;
}

@keyframes heroSlideIn {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.stat-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  transition: var(--transition-normal);
  animation: statCardFloat 3s ease-in-out infinite;
}

.stat-card:nth-child(1) {
  animation-delay: 0s;
}
.stat-card:nth-child(2) {
  animation-delay: 0.5s;
}
.stat-card:nth-child(3) {
  animation-delay: 1s;
}

@keyframes statCardFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Logo Animation */
.logo-container {
  animation: logoFadeIn 0.8s ease-out;
}

@keyframes logoFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Form Animation */
.form-container {
  animation: formSlideIn 0.8s ease-out 0.2s both;
}

@keyframes formSlideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Additional Professional Styles */
.cursor-pointer {
  cursor: pointer;
}

/* Professional Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  line-height: 1.3;
  color: var(--text-primary);
}

.text-gradient {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced Focus States */
.modern-input:focus,
.modern-btn:focus,
.modern-checkbox:focus-within,
.modern-link:focus {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-container {
    padding: 1rem;
  }

  .modern-card {
    margin: 0;
    border-radius: var(--radius-lg);
    padding: 2rem !important;
  }

  .modern-input {
    padding: 16px 20px 16px 48px;
    font-size: 16px;
  }

  .modern-btn {
    padding: 16px 24px;
    font-size: 16px;
    min-height: 52px;
  }

  .hero-panel {
    min-height: 400px;
  }

  .stat-card {
    padding: 1rem;
  }

  .input-icon {
    left: 18px;
  }

  .password-toggle {
    right: 16px;
  }
}

@media (max-width: 576px) {
  .modern-card {
    padding: 1.5rem !important;
  }

  .modern-input-group {
    margin-bottom: 1.25rem;
  }

  .hero-panel {
    min-height: 300px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .modern-card {
    border: 2px solid var(--text-primary);
  }

  .modern-input {
    border-width: 2px;
  }

  .modern-btn {
    border: 2px solid transparent;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Print Styles */
@media print {
  .login-container {
    background: white !important;
  }

  .modern-card {
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }

  .hero-panel {
    display: none !important;
  }
}
