import React from "react";
import { Navigate, Outlet } from "react-router-dom";
import { useSelector } from "react-redux";
import ROUTES from "@constants/routes";

const PrivateRoute = () => {
  const token = useSelector((state) => state.user.token);
  const user = useSelector((state) => state.user.user);
  return token && user ? <Outlet /> : <Navigate to={ROUTES.LOGIN} replace />;
};

export default PrivateRoute;
